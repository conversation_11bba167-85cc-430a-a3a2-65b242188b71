{"home": {"onGoingEvent": "Sự <PERSON>n <PERSON> & Sắp Tới", "viewDetail": "<PERSON>em chi tiết", "bookTicket": "Đặt vé", "outstandingPerformers": "<PERSON><PERSON><PERSON> Nổi B<PERSON>", "outstandingPerformersDescription": "Tr<PERSON>i nghiệm những màn trình diễn đáng nhớ từ các nghệ sĩ tài năng, mang đến âm thanh độc đáo và năng lượng cuốn hút trên sân khấu của chúng tôi.", "pastEvents": "Những Sự <PERSON>", "upcomingEvents": "<PERSON><PERSON><PERSON>", "nowShowingEvents": "<PERSON><PERSON>", "sponsorsAndPartners": "Nhà Tài Trợ Và Đối Tác", "shows": "<PERSON><PERSON><PERSON>", "contact": "<PERSON><PERSON><PERSON>", "noShows": "<PERSON><PERSON><PERSON><PERSON> Trình Sắ<PERSON>"}, "common": {"loading": "<PERSON><PERSON> tả<PERSON>...", "tryAgain": "<PERSON><PERSON><PERSON> lại", "goHome": "Về Trang Chủ", "back": "Quay Lại", "cancel": "Hủy Bỏ", "lastName": "Họ", "firstName": "<PERSON><PERSON><PERSON>", "enterLastName": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> của bạn", "enterFirstName": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enterEmail": "Nhập email"}, "event": {"chooseAttendingDate": "<PERSON><PERSON><PERSON> ngày bạn muốn tham dự", "pleaseChooseAttendingDate": "<PERSON><PERSON> lòng chọn ng<PERSON>y", "ticketInformation": "Thông tin giá vé", "pleaseClickOnTheTicketToBuy": "<PERSON>ui lòng nhấn vào vé bên dưới để mua", "pleaseChooseAttendingDateToViewMapSelectingSeat": "<PERSON><PERSON> lòng chọn ngày tham dự để xem sơ đồ chọn ghế", "choseTickets": "<PERSON><PERSON> đ<PERSON> ch<PERSON>n", "pleaseSelectTicket": "<PERSON>ui lòng chọn vé", "total": "Tổng", "holdSeatAndPay": "Giữ chỗ và Thanh toán", "introduction": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "viewMore": "<PERSON><PERSON>", "viewLess": "Ẩn bớt", "termsAndConditions": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "faq": "Câu hỏi thường gặp", "upcomingSale": "S<PERSON>p mở bán", "areYouReady": "Bạn đã sẵn sàng chưa?", "opportunityMessage": "<PERSON><PERSON> hội không thể bỏ lỡ! Vé sẽ chính thức mở bán trong thời gian tới. Hãy sẵn sàng để sở hữu tấm vé của bạn và trải nghiệm sự kiện đáng mong chờ nhất!", "seat": "Ghế", "confirmOrderAndPayment": "<PERSON><PERSON><PERSON> Đặt <PERSON>", "recipientInfo": "Thông tin người nhận vé", "lastName": "Họ", "firstName": "<PERSON><PERSON><PERSON>", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "enterLastName": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> của bạn", "enterFirstName": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enterEmail": "Nhập email", "lastNameRequired": "<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "firstNameRequired": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "phoneNumberRequired": "<PERSON><PERSON> điện tho<PERSON><PERSON> không đư<PERSON>c để trống", "emailRequired": "<PERSON><PERSON> kh<PERSON>ng đư<PERSON><PERSON> để trống", "emailInvalidFormat": "<PERSON><PERSON> kh<PERSON>ng đúng định dạng", "selectPaymentMethod": "<PERSON><PERSON> toán qua QR hoặc ví điện tử", "orderSummary": "<PERSON><PERSON><PERSON> tắt đơn hàng", "ticketInfo": "Thông tin vé", "enterPromoCode": "<PERSON>hậ<PERSON> mã giảm giá", "applyCode": "<PERSON><PERSON><PERSON> mã", "promoCodeAppliedTo": "<PERSON>ã chỉ được áp dụng cho các hạng vé:", "promoCode": "Mã giảm giá", "totalBeforeDiscount": "Tổng số chưa gi<PERSON>m", "totalAfterDiscount": "<PERSON><PERSON>ng số tiền thanh toán", "confirmAndPay": "<PERSON><PERSON><PERSON> và <PERSON> toán", "confirmAndGoToPayment": "<PERSON><PERSON><PERSON>n và Qua trang thanh toán", "close": "Đ<PERSON><PERSON>", "paymentTerms": "Bằng việc nhấn vào \"<PERSON>ác nhận và Thanh toán\", bạn đồng ý với Điều khoản Dịch vụ và Chính sách Bảo mật của chúng tôi. Vé của bạn sẽ được gửi qua email sau khi thanh toán thành công.", "bankTransferTerms": "<PERSON><PERSON> của bạn sẽ được gửi qua email sau khi chúng tôi xác nhận thanh toán thành công.", "selectDateToAttend": "<PERSON><PERSON><PERSON> ngày bạn muốn tham dự", "termsAndPolicies": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "schedule": "<PERSON><PERSON><PERSON> tr<PERSON>", "bankTransferPayment": "<PERSON><PERSON> toán qua chuyển khoản ngân hàng", "qrCodeMethod": "Cách 1: Mở app ngân hàng / Ví và quét mã QR", "manualTransferMethod": "Cách 2: <PERSON><PERSON><PERSON><PERSON> thủ công theo thông tin", "scanQRToTransfer": "<PERSON><PERSON>t mã này để chuyển tiền", "downloadQR": "<PERSON><PERSON><PERSON>", "transferContent": "Nội dung CK", "bank": "<PERSON><PERSON> h<PERSON>", "beneficiary": "<PERSON><PERSON><PERSON> hưởng", "accountNumber": "Số tài <PERSON>n", "amount": "<PERSON><PERSON> tiền", "copy": "Sao chép", "copied": "Đã sao chép", "transferNote": "Lưu ý: <PERSON><PERSON> <PERSON>hi chuyển khoản thành công. <PERSON>ui lòng nhấn vào button bên dưới để xác nhận bạn đã chuyển tiền", "transactionCode": "Mã giao d<PERSON>ch", "enterTransactionCode": "<PERSON><PERSON><PERSON><PERSON> mã giao d<PERSON>ch", "transactionCodeRequired": "<PERSON><PERSON> giao dịch không đ<PERSON><PERSON><PERSON> để trống", "transactionImage": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "clickToUpload": "<PERSON><PERSON><PERSON> để tải <PERSON>nh", "dragAndDrop": "hoặc kéo thả vào đây", "imageFormat": "PNG, JPG hoặc JPEG (Tối đa 5MB)", "confirmTransfer": "<PERSON><PERSON><PERSON> xác nhận đã chuyển tiền thành công", "confirmRequest": "<PERSON><PERSON><PERSON>n yêu cầu của bạn", "confirmMessage": "Cảm ơn bạn! Chúng tôi sẽ sớm xác nhận và gửi thông tin chi tiết đến email của bạn trong vòng 24 giờ", "qrError": "<PERSON><PERSON> lỗi xảy ra khi hiển thị mã QR. <PERSON>ui lòng tải lại trang hoặc bạn có thể nhập thông tin chuyển khoản bên cạnh để tiến hành thanh toán", "booking": "Đặt chỗ", "stageMap": "Sơ đồ sân khấu", "selectedSeats": "<PERSON><PERSON><PERSON> đã chọn", "enterTicketQuantity": "<PERSON><PERSON> lòng nhập số vé", "quantityMustBeNumber": "<PERSON><PERSON> lòng nhập số", "quantityMustBePositive": "Số lượng phải lớn hơn 0", "quantityMustBeInteger": "Số lượng phải là số nguyên", "nextPagePayment": "<PERSON><PERSON> (QR/Thẻ)", "ticket": "Vé", "availablePromotions": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi có sẵn", "off": "<PERSON><PERSON><PERSON><PERSON>", "currencySymbol": "₫", "requiresMinimumTickets": "<PERSON><PERSON><PERSON> cầu tối thiểu {{count}} vé", "noPromotionsAvailable": "Hiện tại không có khuyến mãi nào", "promotionNotMeetConditions": "<PERSON><PERSON><PERSON> thoả mãn điều kiện tối thiểu để áp dụng mã giảm giá", "dateTime": "<PERSON><PERSON><PERSON>", "event": "<PERSON><PERSON>", "selectTicket": "<PERSON><PERSON><PERSON>", "checkout": "<PERSON><PERSON> toán", "notAvailable": "<PERSON><PERSON><PERSON>ng có sẵn", "selected": "<PERSON><PERSON> ch<PERSON>n", "stage": "<PERSON><PERSON> kh<PERSON>u", "balcony": "<PERSON> công", "ticketPrices": "Giá vé", "discountOnTotalOrderValue": "<PERSON><PERSON> dụng trên tổng giá trị đơn hàng", "discountOnPerOrderItem": "<PERSON><PERSON> dụng trên từng vé", "guideToPayment": "<PERSON><PERSON> <PERSON>hi chọn ghế, vui lòng cuộn xuống để giữ chỗ và tiến hành thanh toán.", "maxPromotions": "Bạn chỉ có thể áp dụng tối đa {{count}} mã giảm giá", "bookNow": "<PERSON><PERSON> vé ngay"}, "seatSelection": {"booking": "Đặt chỗ", "stageMap": "Sơ đồ sân khấu", "selectDatePrompt": "<PERSON><PERSON> lòng chọn ngày tham dự để xem sơ đồ chọn ghế", "selectedTickets": "<PERSON><PERSON> đ<PERSON> ch<PERSON>n", "selectTicketsPrompt": "<PERSON>ui lòng chọn vé", "selectedSeats": "<PERSON><PERSON><PERSON> đã chọn", "total": "Tổng", "holdAndPay": "Giữ chỗ và Thanh toán", "cannotSelectSeat": "<PERSON><PERSON><PERSON>ng thể đặt vì có ghế trống bị bỏ lại giữa hàng", "noEmptySeats": "<PERSON><PERSON>n cần chọn các ghế sát nhau để không làm trống ghế giữa nhé!", "close": "Đ<PERSON><PERSON>", "proceedToPayment": "<PERSON><PERSON><PERSON><PERSON> hành <PERSON>h toán"}, "message": {"errorHoldingSeatAndPayment": "<PERSON><PERSON> lỗi xảy ra khi tiến hành giữ chỗ và thanh toán.", "operationFailed": "<PERSON><PERSON> tác không thành công", "errorOccurred": "Có lỗi xảy ra! Vui lòng thử lại", "promotionFailed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "enterPromoCode": "<PERSON><PERSON> lòng nhập mã giảm giá", "promotionSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "promotionApplied": "Đã áp dụng mã giảm giá", "invalidPromoCode": "Mã giảm giá không hợp lệ", "errorHoldingSeat": "<PERSON><PERSON> lỗi xảy ra khi tiến hành giữ chỗ và thanh toán"}, "errorCode": {"SYS001": "Có lỗi xảy ra! Vui lòng thử lại", "PROMO001": "<PERSON>ã giảm giá không được để trống", "PROMO002": "<PERSON>ã giảm giá {{promotionCode}} kh<PERSON>ng hợp lệ", "PROMO003": "Mã giảm giá {{promotionCode}} đã hết lư<PERSON> sử dụng", "PROMO004": "<PERSON><PERSON><PERSON>ng thể dùng mã giảm giá trước thời gian quy định", "PROMO005": "<PERSON>ã giảm giá đã hết hạn", "PROMO006": "<PERSON><PERSON><PERSON> thoả mãn số lượng vé tối thiểu để áp dụng mã giảm giá", "PROMO007": "Bạn đã dùng hết số lượt đư<PERSON><PERSON> áp dụng cho giảm giá {{promotionCode}}", "PROMO008": "<PERSON><PERSON><PERSON> thoả mãn số lượng vé tối thiểu để áp dụng mã giảm giá {{promotionCode}}", "PROMO009": "<PERSON><PERSON><PERSON><PERSON> thể áp dụng nhiều mã giảm giá cho một đơn hàng", "PROMO010": "Chỉ cho phép áp dụng tối đa {{maxAppliedPromotions}} mã giảm giá cho một đơn hàng", "PROMO011": "Mã giảm giá {{promotionCodes}} kh<PERSON>ng hợp lệ", "EVT001": "<PERSON><PERSON> kiện không được để trống", "EVT002": "<PERSON><PERSON> kiện không tồn tại", "EVT003": "<PERSON>ự kiện chưa được mở bán", "EVT004": "Sự kiện đã đóng bán", "EVT005": "<PERSON>ự kiện đã bị hủy", "EVT006": "<PERSON><PERSON><PERSON> tham gia sự kiện không đư<PERSON><PERSON> để trống", "EVT007": "<PERSON><PERSON><PERSON> tham dự sự kiện {{eventTitle}} kh<PERSON>ng đúng", "EVT008": "Event ID không đ<PERSON><PERSON><PERSON> để trống", "EVT009": "Event schedule ID kh<PERSON>ng đ<PERSON><PERSON><PERSON> để trống", "SEAT001": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "SEAT002": "Ghế {{seats}} đang được giữ bởi người khác! Vui lòng chọn ghế khác", "SEAT003": "Ghế {{seats}} hiện đã được đặt. <PERSON><PERSON> lòng chọn ghế khác.", "SEAT004": "Phiên chữ chỗ đã hết hạn! Vui lòng chọn lại ghế và thực hiện lại", "SEAT005": "Ghế {{seat}} bị lặp! Vui lòng kiểm tra lại", "TICK001": "Loại vé không đư<PERSON>c để trống", "TICK002": "<PERSON>ê<PERSON> lo<PERSON>i vé không đư<PERSON>c để trống", "TICK003": "Số lượng vé phải là số nguyên dương", "TICK004": "Loại vé không tồn tại", "TICK005": "Vé {{ticketClass}} hiện đã được đặt hết! Vui lòng chọn vé khác.", "TICK006": "Vé {{ticketClass}} hiện chỉ còn tối đa {{remaining}} vé!. Vui lòng nhập lại số lượng mua", "TICK007": "Loại vé không tồn tại cho sự kiện {{eventTitle}}", "TICK008": "Ghế hạng vé {{ticketClass}} cho ngày đã chọn đã được đặt hết! Vui lòng chọn chỗ ngồi hạng vé khác", "TICK009": "G<PERSON>ế hạng vé {{ticketClass}} cho ngày đã chọn chỉ còn tối đa {{remaining}} vé! Vui lòng chọn lại", "TICK010": "Ticket Price ID không đư<PERSON><PERSON> để trống", "BOOK001": "Invalid booking type", "ORD001": "<PERSON><PERSON> mục đơn hàng không đư<PERSON><PERSON> để trống", "ORD002": "Đơn hàng không tồn tại", "ORD003": "Đ<PERSON><PERSON> hàng đã đ<PERSON><PERSON><PERSON> toán", "ORD004": "<PERSON><PERSON><PERSON> hàng đã hết hạn thanh toán", "ORD005": "<PERSON><PERSON> lòng chọn ghế và thực hiện lại thao tác", "ORD006": "<PERSON><PERSON> tiền điều chỉnh không hợp lệ", "ORD007": "<PERSON><PERSON> tiền điều chỉnh không được âm", "CHECKIN001": "<PERSON><PERSON><PERSON><PERSON> tìm thấy vé", "CHECKIN002": "<PERSON><PERSON><PERSON> chưa được chỉ định cho vé", "CHECKIN003": "Vé này đã đư<PERSON><PERSON> check-in", "CHECKIN004": "Check-in thất bại", "CHECKIN005": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "CHECKIN006": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin check-in", "CHECKIN007": "Admin ID và ít nhất một mã vé là bắt buộc", "CHECKIN008": "<PERSON><PERSON><PERSON><PERSON> tìm thấy admin", "CHECKIN009": "Tất cả các mã vé phải thuộc cùng một người dùng", "CHECKIN010": "Email và mã vé là bắt buộc", "CHECKIN011": "Mã vé không hợp lệ", "CHECKIN012": "Email không khớp với thông tin vé", "CHECKIN013": "Mã vé là bắt buộc", "CHECKIN014": "<PERSON><PERSON> lòng chọn sự kiện", "CUS001": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "CUS002": "<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "CUS003": "<PERSON><PERSON> điện tho<PERSON><PERSON> không đư<PERSON>c để trống", "CUS004": "<PERSON><PERSON> kh<PERSON>ng đư<PERSON><PERSON> để trống", "UNAUTHORIZED": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "error": {"title": "Rất tiếc! Đã xảy ra lỗi", "description": "<PERSON><PERSON><PERSON> lo, chúng tôi đã ghi nhận lỗi và sẽ sớm khắc phục. <PERSON><PERSON>n có thể nhấn thử lại hoặc quay về trang chủ.", "tryingAgain": "<PERSON><PERSON> thử lại", "tryingAgainDescription": "<PERSON><PERSON> cố gắng khôi phục từ lỗi", "goingHome": "<PERSON><PERSON> về trang chủ", "goingHomeDescription": "<PERSON><PERSON> chuy<PERSON>n hướng về trang chủ"}, "checkout": {"personalInfo": "Thông tin cá nhân", "lastName": "Họ", "firstName": "<PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "orderSummary": "<PERSON><PERSON><PERSON> tắt đơn hàng", "ticketInfo": "Thông tin vé", "promoCode": "Mã giảm giá", "enterPromoCode": "<PERSON>hậ<PERSON> mã giảm giá", "apply": "<PERSON><PERSON>", "subtotal": "<PERSON><PERSON><PERSON>", "discount": "G<PERSON>ảm giá", "total": "<PERSON><PERSON><PERSON> cộng", "termsAgreement": "Tôi đồng ý với Điều khoản và Điều kiện và Chính sách Bảo mật. Tôi hiểu rằng vé của tôi sẽ được gửi đến email của tôi sau khi thanh toán được xác nhận.", "confirmAndPay": "<PERSON><PERSON><PERSON> n<PERSON>n và thanh toán", "disclaimer": "Vé của bạn sẽ được gửi đến email của bạn sau khi thanh toán thành công. <PERSON>ui lòng kiểm tra hộp thư đến email của bạn (và thư mục spam) để xác nhận vé.", "goingHomeDescription": "<PERSON><PERSON> chuy<PERSON>n hướng về trang chủ", "loginFailed": "<PERSON><PERSON> xảy ra lỗi trong quá trình đăng nhập", "loadEventFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải sự kiện", "failedToLoadEvents": "<PERSON><PERSON><PERSON><PERSON> thể tải sự kiện", "failedToLoadHistory": "<PERSON><PERSON><PERSON><PERSON> thể tải lịch sử check-in", "failedToCheckIn": "Check-in thất bại", "bulkCheckInFailed": "Check-in hàng loạt thất bại", "bulkMarkGivenFailed": "<PERSON><PERSON><PERSON> dấu hàng loạt đã giao thất bại", "unexpectedError": "Lỗi không xác định"}, "checkin": {"adminCheckIn": "<PERSON><PERSON>", "signInToContinue": "<PERSON><PERSON><PERSON> nhập để tiếp tục", "email": "Email", "enterYourEmail": "Nhập email c<PERSON><PERSON> bạn", "password": "<PERSON><PERSON><PERSON>", "enterYourPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u của bạn", "signIn": "<PERSON><PERSON><PERSON>", "signingIn": "<PERSON><PERSON> đăng nhập...", "pleaseEnterEmailAndPassword": "<PERSON><PERSON> lòng nh<PERSON><PERSON> cả email và mật khẩu", "login": "<PERSON><PERSON><PERSON>", "loadingEvents": "<PERSON><PERSON> tải sự kiện...", "pleaseLoginFirst": "<PERSON><PERSON> lòng đăng nhập trước", "pleaseSelectEventAndSchedule": "<PERSON><PERSON> lòng chọn một sự kiện và lịch trình", "noSchedulesAvailable": "<PERSON><PERSON><PERSON><PERSON> có lịch trình nào cho sự kiện này", "selected": "<PERSON><PERSON> ch<PERSON>n", "selectEvent": "<PERSON><PERSON><PERSON> sự kiện", "confirm": "<PERSON><PERSON><PERSON>", "checkInHistory": "<PERSON><PERSON><PERSON> s<PERSON> check-in", "searchByTicketCodeOrSeat": "T<PERSON>m kiếm theo mã vé hoặc ghế...", "back": "Quay lại", "loadingCheckInHistory": "<PERSON><PERSON> t<PERSON><PERSON> lịch sử check-in...", "noRecordsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bản ghi nào", "delete": "Xóa", "confirmDeleteRecord": "Bạn có chắc chắn muốn xóa bản ghi này không?", "recordDeletedSuccessfully": "<PERSON><PERSON><PERSON> ghi đã đư<PERSON><PERSON> xóa thành công", "failedToDeleteRecord": "<PERSON><PERSON><PERSON> bản ghi thất bại", "ticketUsed": "VÉ ĐÃ SỬ DỤNG", "validTicket": "VÉ HỢP LỆ", "ticketDetails": "CHI TIẾT VÉ", "name": "Tên:", "event": "Sự kiện:", "date": "Ngày:", "emailLabel": "Email:", "phoneNumber": "<PERSON><PERSON> điện thoại:", "ticketType": "Loại vé:", "seat": "Ghế:", "checkedInAt": "Check-in lúc:", "by": "Bởi:", "checkInNow": "CHECK-IN NGAY", "checkingIn": "ĐANG CHECK-IN...", "alreadyCheckedIn": "ĐÃ CHECK-IN", "invalidTicketData": "<PERSON><PERSON> liệu vé không hợp lệ", "ticketCheckedInSuccessfully": "Check-in vé thành công", "failedToCheckIn": "Check-in thất bại", "enterTicketCodeOrSeat": "Nhập mã vé/Ghế", "validateTicket": "<PERSON><PERSON><PERSON> thực vé", "validating": "<PERSON><PERSON> xác thực...", "viewHistory": "<PERSON><PERSON> tất cả checkin", "multipleTicketsFound": "<PERSON><PERSON><PERSON> thấy nhi<PERSON>u vé", "multipleTicketsFoundLabel": "<PERSON><PERSON><PERSON> thấy nhi<PERSON>u vé", "pleaseEnterTicketCode": "<PERSON><PERSON> lòng nhập mã vé", "ticketAlreadyCheckedIn": "Vé này đã đư<PERSON><PERSON> check-in", "ticketNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy vé", "schedule": "<PERSON><PERSON><PERSON> trình:", "ticketPriceInfo": "Thông tin giá vé:", "ticketPriceInfoLabel": "Thông tin giá vé:", "checkedIn": "Đã check-in:", "used": "Đã sử dụng", "valid": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "byUsher": "Bởi Usher"}, "customerCheckinTicket": {"ticketCheckIn": "Tự Check In", "enterYourEmail": "Nhập email c<PERSON><PERSON> bạn", "enterYourTicketCode": "<PERSON><PERSON>ậ<PERSON> mã vé của bạn", "checkIn": "Check-in", "checkingIn": "Đang check-in...", "alreadyCheckedIn": "Đã check-in truớc đó", "showTicket": "<PERSON><PERSON><PERSON> đưa màn hình cho nhân viên để nhận vé!", "confirmedSuccessfully": "<PERSON><PERSON> xác nhận thành công!", "zone": "<PERSON><PERSON> vực:", "ticketCode": "Mã vé:", "seat": "Ghế:", "eventName": "<PERSON><PERSON><PERSON> sự kiện:", "email": "Email:", "attendeeName": "<PERSON><PERSON><PERSON> ng<PERSON> tham dự:", "checkedInAt": "Check-in lúc:", "ticketGiven": "<PERSON><PERSON> đã đ<PERSON><PERSON><PERSON> giao", "confirmValidTicket": "<PERSON><PERSON><PERSON> vé hợp lệ và giao vé", "checkInAnotherTicket": "Check-in vé kh<PERSON>c", "sisterTickets": "<PERSON><PERSON> liên quan", "noSisterTickets": "Không có vé liên quan nào", "bulkCheckIn": "Check-in hàng lo<PERSON>t", "bulkMarkGiven": "<PERSON> phép checkin đồng thời nhiều vé", "checkInSelected": "Check-in <PERSON><PERSON> đã chọn", "markSelectedAsGiven": "<PERSON><PERSON><PERSON>n vé liên quan hợp lệ và giao vé", "cancelBulk": "<PERSON><PERSON><PERSON>nh động hàng lo<PERSON>t", "noSelection": "<PERSON><PERSON><PERSON><PERSON> có lựa chọn", "selectAtLeastOneSisterTicket": "<PERSON><PERSON><PERSON> ít nhất một vé liên quan", "selectAtLeastOneTicket": "<PERSON><PERSON><PERSON> ít nhất một vé", "bulkCheckInSuccess": "Check-in hàng lo<PERSON>t", "checkedInSelectedTickets": "Đã check-in các vé đ<PERSON><PERSON><PERSON> ch<PERSON>n", "bulkMarkGivenSuccess": "<PERSON><PERSON><PERSON> dấu hàng loạt đã giao vé thành công", "markedSelectedTicketsAsGiven": "<PERSON><PERSON><PERSON> vé liên quan đã chọn đư<PERSON><PERSON> checkin thành công", "confirmTicketGiven": "<PERSON><PERSON><PERSON> nhận vé này đã được giao bởi admin \"{{adminId}}\"?", "confirmed": "Đã nhận vé", "success": "<PERSON><PERSON><PERSON><PERSON> công", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "deselectAll": "Bỏ chọn tất cả"}, "userprofile": {"title": "<PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>", "processing": "<PERSON><PERSON>", "cancelled": "Đã <PERSON>", "upcoming": "<PERSON><PERSON><PERSON>", "finished": "Đ<PERSON>", "month04": "Tháng 4", "ticketTitle": "<PERSON><PERSON>", "statusSuccess": "<PERSON><PERSON><PERSON><PERSON>", "eTicket": "<PERSON><PERSON>", "orderCode": "<PERSON>ã Đ<PERSON>", "dateRange": "09:00, <PERSON><PERSON><PERSON> 24 Tháng 4, 2025 – 17:00, <PERSON><PERSON><PERSON> 26 Tháng 4, 2025", "location": "Trung Tâm <PERSON> và <PERSON>ển <PERSON> (SECC), 799 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 7, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "seat": "Chỗ <PERSON><PERSON>i", "ticketPrice": "Loại Vé", "statusProcessing": "<PERSON><PERSON>", "statusHold": "<PERSON><PERSON><PERSON>", "statusCancelled": "Đã <PERSON>", "userLogin": "<PERSON><PERSON><PERSON>", "accountSettings": {"infoHelper": "<PERSON><PERSON> cấp thông tin chính xác sẽ giúp hỗ trợ bạn khi mua vé hoặc khi cần xác minh vé.", "fullName": "Họ và Tên", "enterFullName": "<PERSON><PERSON><PERSON><PERSON> họ và tên của bạn", "phone": "Số Điệ<PERSON>", "enterPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại của bạn", "email": "Email", "enterEmail": "Nhập email c<PERSON><PERSON> bạn", "dob": "<PERSON><PERSON><PERSON>", "enterDob": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>y sinh c<PERSON>a bạn", "gender": "Gi<PERSON><PERSON>", "selectGender": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> t<PERSON>h", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>"}, "sidebar": {"accountOf": "<PERSON><PERSON><PERSON>", "accountSettings": "Cài Đặt <PERSON>à<PERSON>", "accountInfo": "Thông Tin T<PERSON>", "purchasedTickets": "<PERSON><PERSON>", "myEvents": "<PERSON><PERSON>"}, "myEvents": "<PERSON><PERSON> kiện của tôi", "noEvents": "Bạn chưa có sự kiện nào", "upcomingTickets": "<PERSON><PERSON> sự kiện sắp diễn ra", "pastTickets": "<PERSON><PERSON> sự kiện đã qua", "noUpcomingTickets": "<PERSON><PERSON><PERSON><PERSON> có vé sự kiện sắp diễn ra", "noPastTickets": "<PERSON><PERSON><PERSON><PERSON> có vé sự kiện đã qua", "page": "Trang {{current}} / {{total}}"}, "auth": {"signInSuccessfully": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "pleaseEnterEmail": "<PERSON><PERSON> lòng nhập email", "forgotPasswordRequestSent": "<PERSON><PERSON><PERSON> cầu đặt lại mật khẩu đã đượ<PERSON> gửi", "checkYourEmail": "<PERSON><PERSON> lòng kiểm tra email của bạn để nhận liên kết đặt lại mật khẩu", "failedToSendRequest": "<PERSON><PERSON><PERSON> yêu cầu thất bại", "somethingWentWrong": "Đ<PERSON> xảy ra lỗi", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "enterEmailForReset": "Nhập email của bạn để nhận liên kết đặt lại mật khẩu", "email": "Email", "password": "<PERSON><PERSON><PERSON>", "enterYourEmail": "Nhập email c<PERSON><PERSON> bạn", "sendingRequest": "<PERSON><PERSON> g<PERSON>i yêu cầu...", "sendResetLink": "<PERSON><PERSON><PERSON> liên kết đặt lại mật khẩu", "backToLogin": "Quay lại đăng nh<PERSON>p", "missingToken": "T<PERSON><PERSON>u token", "invalidResetLink": "<PERSON><PERSON><PERSON> kết đặt lại mật khẩu không hợp lệ", "pleaseEnterNewPassword": "<PERSON><PERSON> lòng nhập mật khẩu mới của bạn", "passwordsDoNotMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "passwordResetSuccessful": "Đặt lại mật khẩu thành công", "failedToResetPassword": "Đặt lại mật khẩu thất bại", "resetPassword": "Đặt lại mật khẩu", "enterNewPassword": "<PERSON><PERSON><PERSON><PERSON> mật khẩu mới của bạn", "newPassword": "<PERSON><PERSON><PERSON> mới", "confirmNewPassword": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "resettingPassword": "<PERSON><PERSON> đặt lại mật khẩu...", "enterYourPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u của bạn", "signIn": "<PERSON><PERSON><PERSON>", "signInToContinue": "<PERSON><PERSON><PERSON> nhập để tiếp tục", "passwordMinLength": "<PERSON><PERSON>t khẩu phải có ít nhất {{length}} ký tự", "passwordStrength": "<PERSON><PERSON>t khẩu phải chứa ít nhất hai trong các loại ký tự sau: chữ hoa, chữ thường, số hoặc ký tự đặc biệt", "firstTimeLoginGuideline": "Nếu bạn đã từng đặt vé trước đây, bạn đã có tài khoản. Chỉ cần dùng email của bạn để đặt lại mật khẩu, đăng nhập và xem thông tin vé cùng các thông tin khác.", "firstTimeLoginLink": "<PERSON>ần đầu đăng nhập? Bấm vào đây", "firstTimeLoginHeader": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "firstTimeLoginEmailPlaceholder": "Nhập email bạn đã dùng để đặt vé", "pleaseEnterEmailAndPassword": "<PERSON><PERSON> lòng nh<PERSON><PERSON> cả email và mật khẩu", "signingIn": "<PERSON><PERSON> đăng nhập..."}}