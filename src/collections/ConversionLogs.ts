import type { CollectionConfig } from 'payload'

const ConversionLogs: CollectionConfig = {
  slug: 'conversion-logs',
  admin: { useAsTitle: 'affiliate' },
  fields: [
    {
      name: 'affiliate',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'link',
      type: 'relationship',
      relationTo: 'affiliate-links',
      required: true,
    },
    {
      name: 'timestamp',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
    },
    {
      name: 'orderId',
      type: 'text',
      required: false,
    },
    {
      name: 'conversionAmount',
      type: 'number',
      required: false,
    },
    {
      name: 'commission',
      type: 'number',
      required: false,
    },
  ],
};

export default ConversionLogs; 