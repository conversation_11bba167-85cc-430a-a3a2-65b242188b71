import type { CollectionConfig } from 'payload'

const ClickLogs: CollectionConfig = {
  slug: 'click-logs',
  admin: { useAsTitle: 'affiliate' },
  fields: [
    {
      name: 'affiliate',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'link',
      type: 'relationship',
      relationTo: 'affiliate-links',
      required: true,
    },
    {
      name: 'timestamp',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
    },
    {
      name: 'ip',
      type: 'text',
      required: false,
    },
    {
      name: 'location',
      type: 'text',
      required: false,
    },
    {
      name: 'referrer',
      type: 'text',
      required: false,
    },
  ],
};

export default ClickLogs; 