import type { CollectionConfig } from 'payload'

const AffiliatePayouts: CollectionConfig = {
  slug: 'affiliate-payouts',
  admin: { useAsTitle: 'user' },
  fields: [
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'amount',
      type: 'number',
      required: true,
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Paid', value: 'paid' },
        { label: 'Rejected', value: 'rejected' },
      ],
      defaultValue: 'pending',
    },
    {
      name: 'note',
      type: 'textarea',
      required: false,
    },
    {
      name: 'bankInfo',
      type: 'text',
      required: false,
    },
    {
      name: 'period',
      type: 'text',
      required: false,
      admin: { description: 'Payout period (e.g. 2024-05)' },
    },
  ],
};

export default AffiliatePayouts; 