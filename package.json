{"name": "orchestars-vn", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation SKIP_PAYLOAD_INIT=true next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev --turbo", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "migrate:create": "pnpm run payload migrate:create ", "migrate": "pnpm run payload migrate", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "ci": "cross-env NODE_OPTIONS=--no-deprecation payload migrate && pnpm build", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:e2e": "node scripts/run-playwright.js", "test:e2e:ui": "node scripts/run-playwright.js --ui", "test:e2e:debug": "node scripts/run-playwright.js --debug", "test:e2e:report": "playwright show-report"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@mezh-hq/react-seat-toolkit": "^3.2.5", "@payloadcms/admin-bar": "^3.30.0", "@payloadcms/db-postgres": "^3.30.0", "@payloadcms/email-nodemailer": "^3.30.0", "@payloadcms/email-resend": "^3.30.0", "@payloadcms/live-preview-react": "^3.30.0", "@payloadcms/next": "^3.30.0", "@payloadcms/payload-cloud": "^3.30.0", "@payloadcms/plugin-form-builder": "^3.30.0", "@payloadcms/plugin-import-export": "^3.30.0", "@payloadcms/plugin-nested-docs": "^3.30.0", "@payloadcms/plugin-redirects": "^3.30.0", "@payloadcms/plugin-search": "^3.30.0", "@payloadcms/plugin-sentry": "^3.30.0", "@payloadcms/plugin-seo": "^3.30.0", "@payloadcms/richtext-lexical": "^3.30.0", "@payloadcms/storage-s3": "^3.30.0", "@payloadcms/storage-vercel-blob": "^3.30.0", "@payloadcms/translations": "^3.31.0", "@payloadcms/ui": "^3.30.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sentry/nextjs": "^9.14.0", "@tailwindcss/postcss": "4.0.10", "@tanstack/react-query": "^5.56.2", "@types/joi": "^17.2.3", "@vercel/analytics": "^1.5.0", "axios": "^1.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "embla-carousel-react": "^8.3.0", "geist": "^1.3.0", "graphql": "^16.8.2", "input-otp": "^1.2.4", "joi": "^17.13.3", "jose": "5.9.6", "js-cookie": "^3.0.5", "lucide-react": "^0.477.0", "next": "15.2.3", "next-sitemap": "^4.2.3", "next-themes": "^0.3.0", "nodemailer": "^7.0.3", "payload": "3.30.0", "prism-react-renderer": "^2.3.1", "qrcode": "^1.5.4", "qs": "^6.14.0", "qs-esm": "^7.0.2", "react": "19.0.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-facebook-pixel": "^1.0.4", "react-hook-form": "7.45.4", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sharp": "0.32.6", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@axe-core/playwright": "^4.10.1", "@eslint/eslintrc": "^3.2.0", "@faker-js/faker": "^9.7.0", "@playwright/test": "^1.52.0", "@tailwindcss/typography": "^0.5.13", "@types/crypto-js": "^4.2.2", "@types/escape-html": "^1.0.2", "@types/express": "^5.0.1", "@types/js-cookie": "^3.0.6", "@types/node": "22.5.4", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.18", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/supertest": "^6.0.3", "@vitest/coverage-v8": "^3.1.3", "@vitest/ui": "^3.1.3", "autoprefixer": "^10.4.19", "copyfiles": "^2.4.1", "dotenv": "^16.5.0", "eslint": "^9.16.0", "eslint-config-next": "15.2.3", "express": "^5.1.0", "msw": "^2.7.6", "postcss": "^8.4.38", "prettier": "^3.4.2", "supabase": "^2.22.12", "supertest": "^7.1.0", "tailwindcss": "^3.4.17", "typescript": "5.7.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3", "vitest-mock-extended": "^3.1.0"}, "packageManager": "pnpm@9.15.5", "engines": {"node": "^22.14.0"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}